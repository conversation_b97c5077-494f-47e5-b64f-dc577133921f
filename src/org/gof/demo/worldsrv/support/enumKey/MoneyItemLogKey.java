package org.gof.demo.worldsrv.support.enumKey;

/** 
 * 道具货币日志类型 
 * <AUTHOR>
 * @Date 2024/3/4
 * @Param
 */
public enum MoneyItemLogKey {
	未设置(0),
	测试接口(1),
	充值(2),
	获取邮件附件(3),
	职业重置消耗(4),
	打开神灯消耗(5),
	主线任务(6),
	类型13开礼包(7),
	商店购买(8),
	抽技能(9),
	抽同伴(10),
	技能解锁(11),
	技能升级(12),
	同伴解锁(13),
	同伴升级(14),
	装备售出(15),
	神器升级(16),
	符石获得(17),
	神器宝石升级(18),
	坐骑升级(19),
	坐骑皮肤升级(20),
	坐骑天赋升级(21),
	翅膀羽毛升级(22),
	翅膀天赋升级(23),
	翅膀天赋重置(24),
	翅膀皮肤升级(25),
	翅膀图鉴激活(26),
	武魂升级(27),
	武魂祈福(28),
	武魂分解(29),
	武魂重置(30),
	武魂获得(31),
	遗物升级(32),
	遗物寻找(33),
	通关副本类型1(34),
	通关副本类型2(35),
	通关副本类型3(36),
	通关副本类型8(37),
	通关副本类型9(38),
	通关副本类型22(39),
	通关副本类型28(40),
	通关副本类型29(41),
	通关副本类型30(42),
	红包(43),
	挖矿(44),
	镐子恢复(45),
	科技升级(46),
	创宗(47),
	公会捐赠(48),
	每日任务活跃度奖励(49),
	星钻商店首充奖励(50),
	星钻商店充值奖励(51),
	钻石商店首充奖励(52),
	钻石商店充值奖励(53),
	合成(54),
	加速(55),
	放置奖励(56),
	皮肤解锁(57),
	皮肤升级(58),
	限时商店定时奖励(59),
	限时商店礼包奖励(60),
	充值免费奖励(61),
	公会boss奖励(62),
	称号获得(63),
	头像框获得(64),
	气泡获得(65),
	神器皮肤升级(66),
	公会帮助(67),
	公会宝箱活动奖励(68),
	职业觉醒升级(69),
	职业觉醒(70),
	职业重置返还(71),
	职业觉醒道具转换(72),
	好友金币领取(73),
	公会boss宝箱奖励(74),
	世界boss每日首次奖励(75),
	每日恢复(76),
	组队副本(77),
	武魂融合(78),
	通关副本类型11(79),
	家园种植(80),
	家园施肥(81),
	家园偷菜(82),
	家园收获(83),
	建筑升级(84),
	雕像刷新(85),
	武魂日常奖励(86),
	功能预告奖励(87),
	家园升级(88),
	改名(89),
	特权卡每日奖励(90),
	特权卡礼包奖励(91),
	战令普通奖励(92),
	战令高级奖励(93),
	战令额外奖励(94),
	道具恢复(95),
	好友肥料恢复(96),
	种子恢复(97),
	家园施肥回滚(97),
	购买竞技场门票(98),
	定级赛奖励(99),
	公会旗帜修改(100),
	击杀掉落(101),
	公会改名消耗(102),
	坐骑改装升级(103),
	停车奖励(104),
	停车抢占奖励(105),
	装扮升级(106),
	挑战竞技场(107),
	竞技场胜利奖励(108),
	挑战排位赛(109),

	挑战排位赛奖励(110),
	活动完美每轮(111),
	活动任务奖励(112),

	公会Boss挑战道具恢复(113),
	公会boss(114),

	每日任务(115),
	冒险任务(116),
	转盘活动(117),
	中秋搜寻(118),
	砸金蛋(119),
	登录活动(120),
	转盘(121),
	七日登录(122),
	招财猫(123),
	充值失败(124),
	充值礼包(125),
	基金(126),
	成长之路(127),
	创角首次登录奖励(128),
	道具使用(129),
	回收(130),
	暗黑试炼(131),
	充值超次数补奖励(132),
	SNS_分享(133),
	无特权战斗加速消耗(134),

	GM后台操作(135),
	乱斗积分(136),
	七夕送礼(137),
	勇者试炼(138),
	七日试炼(139),

	圣诞献礼(140),
	宝箱活动(141),
	趣味竞答答题(142),
	趣味竞答摇骰子(143),
	全民赶年兽(144),
	成就任务(145),
	成就统计奖励(146),
	装扮升级返还(147),
	超值卡(148),
	跨服战选择职业(149),
	跨服战入侵消耗(150),
	跨服战放置奖励(151),
	跨服战防守击杀玩家奖励(152),
	跨服战防守击杀怪物奖励(153),
	跨服战复活消耗(154),
	限时礼盒(155),
	浇灌爱情树(156),
	停车场收益(157),
	道具修复(158 ),
	飞宠升级(159),
	飞宠进阶(160),
	飞宠重置(161),
	飞宠分解(162),
	飞宠改名(163),
	飞宠培养(164),
	飞宠成就(165),
	飞宠转生(166),
	飞宠初始化(167),
	双人本通关(168),
	武道会竞猜消耗(169),
	武道会竞猜获得(170),
	武道会任务(171),
	每周道具恢复(172),
	武道会膜拜获得(173),
	武道会回收竞猜币(174),
	武道会发放竞猜积分(175),
	军团入侵(176),
	星将升级(177),
	星将抽卡(178),
	星将抽卡兑换(179),
    星将物品兑换(180),
	在线放置奖励(181),
	离线放置奖励(182),
	回归登录奖励(183),
	回归任务(184),
	大富翁(185),
	史莱姆融合(186),
	排行榜点赞奖励(187),
	星将激活(188),
	史莱姆(189),
	史莱姆扫荡(190),
	多档礼包活动(191),
	累充免费奖励(192),
	累充每日奖励(193),
	累充奖励(194),
	雕像消耗(195),
	活动日历每日免费礼包(196),
	黄金塔(197),
	超上限恢复(198),
	小游戏首通奖励(199),
	小游戏累计奖励(200),
	大战魔王城(201),
	阵营对抗(202),
	装备打造(203),
	转生魔剑(204),
	皮肤试用(205),
	翻牌(206),
	团购礼包(207),
	合服道具提交(208),
	公会充值活动(209),
	美观值等级奖励(210),
	美观值点赞奖励(211),
	钓鱼养成(212),
	钓鱼槽位解锁升级(213),
	钓鱼收竿(214),
	钓鱼自动收竿(215),
	钓鱼图鉴升级(216),
	钓鱼渔具升级(217),
	钓鱼每日任务奖励(218),
	钓鱼渔场解锁(219),
    ;
	/** 日志类型值 **/
	private int type;

	private MoneyItemLogKey(int type) {
		this.type = type;
	}

	public int getType() {
		return type;
	}

}